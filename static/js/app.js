// Security Scanner Web Interface - JavaScript

class SecurityScannerApp {
    constructor() {
        this.currentScanId = null;
        this.pollingInterval = null;
        this.pollDelay = 2000; // 2 segundos
        this.progressValue = 0;
        this.tools = ['whois', 'dig', 'theharvester', 'assetfinder', 'nuclei'];
        this.toolsCompleted = 0;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.showInitialMessage();
        console.log('Security Scanner App initialized');
    }
    
    bindEvents() {
        // Evento del formulario de escaneo
        const scanForm = document.getElementById('scanForm');
        if (scanForm) {
            scanForm.addEventListener('submit', (e) => this.handleScanSubmit(e));
        }
        
        // Botón de descarga de reporte
        const downloadBtn = document.getElementById('downloadReportBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadReport());
        }
        
        // Validación en tiempo real del input
        const targetInput = document.getElementById('targetInput');
        if (targetInput) {
            targetInput.addEventListener('input', (e) => this.validateInput(e));
        }
    }
    
    validateInput(event) {
        const input = event.target;
        const value = input.value.trim();
        
        // Expresión regular para validar dominios
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
        
        if (value && !domainRegex.test(value)) {
            input.classList.add('is-invalid');
            input.classList.remove('is-valid');
        } else if (value) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
        } else {
            input.classList.remove('is-invalid', 'is-valid');
        }
    }
    
    async handleScanSubmit(event) {
        event.preventDefault();
        
        const targetInput = document.getElementById('targetInput');
        const target = targetInput.value.trim();
        
        if (!target) {
            this.showAlert('Por favor ingresa un dominio válido', 'warning');
            return;
        }
        
        try {
            await this.startScan(target);
        } catch (error) {
            console.error('Error starting scan:', error);
            this.showAlert('Error al iniciar el análisis: ' + error.message, 'danger');
        }
    }
    
    async startScan(target) {
        const startBtn = document.getElementById('startScanBtn');
        const scanInfo = document.getElementById('scanInfo');
        const resultsSection = document.getElementById('resultsSection');
        const initialMessage = document.getElementById('initialMessage');
        
        // Deshabilitar botón y mostrar loading
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Iniciando...';
        
        try {
            const response = await fetch('/scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ target: target })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Error al iniciar el escaneo');
            }
            
            this.currentScanId = data.scan_id;
            
            // Mostrar información del escaneo
            document.getElementById('scanId').textContent = `ID: ${data.scan_id}`;
            scanInfo.style.display = 'block';
            
            // Ocultar mensaje inicial y mostrar resultados
            if (initialMessage) initialMessage.style.display = 'none';
            if (resultsSection) {
                resultsSection.style.display = 'block';
                resultsSection.classList.add('fade-in');
            }
            
            // Iniciar polling para obtener resultados
            this.startPolling();
            
            this.showAlert(data.message, 'success');
            
        } catch (error) {
            console.error('Error:', error);
            this.showAlert(error.message, 'danger');
            
            // Rehabilitar botón
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-search"></i> Iniciar Análisis Completo';
        }
    }
    
    startPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
        
        this.pollingInterval = setInterval(() => {
            this.fetchScanStatus();
        }, this.pollDelay);
        
        // Primera llamada inmediata
        this.fetchScanStatus();
    }
    
    async fetchScanStatus() {
        if (!this.currentScanId) return;
        
        try {
            const response = await fetch(`/status/${this.currentScanId}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Error al obtener estado del escaneo');
            }
            
            this.updateUI(data);
            
            // Si el escaneo está completo, detener polling
            if (data.status === 'completed' || data.status === 'completed_with_errors' || data.status === 'error') {
                this.stopPolling();
                this.onScanCompleted(data);
            }
            
        } catch (error) {
            console.error('Error fetching scan status:', error);
            this.showAlert('Error al obtener estado del análisis: ' + error.message, 'warning');
        }
    }
    
    updateUI(data) {
        this.updateProgress(data);
        this.updateVulnerabilities(data.vulnerabilities || []);
        this.updateDNSInfo(data.tools_results);
        this.updateWhoisInfo(data.tools_results);
        this.updateSubdomains(data.tools_results);
        this.updateEmails(data.tools_results);
        this.updateRecommendations(data.recommendations || []);
    }
    
    updateProgress(data) {
        const progressBar = document.getElementById('progressBar');
        const currentTool = document.getElementById('currentTool');
        
        // Calcular progreso basado en herramientas completadas
        let completed = 0;
        const toolsResults = data.tools_results || {};
        
        this.tools.forEach(tool => {
            if (toolsResults[tool] && 
                (toolsResults[tool].status === 'completed' || 
                 toolsResults[tool].status === 'error' || 
                 toolsResults[tool].status === 'timeout')) {
                completed++;
            }
        });
        
        const progress = Math.round((completed / this.tools.length) * 100);
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
        
        // Actualizar herramienta actual
        if (currentTool) {
            if (data.status === 'completed') {
                currentTool.textContent = 'Análisis completado!';
            } else if (data.status === 'error') {
                currentTool.textContent = 'Error en el análisis';
            } else {
                const runningTool = this.tools.find(tool => {
                    const result = toolsResults[tool];
                    return result && result.status === 'running';
                });
                
                if (runningTool) {
                    currentTool.textContent = `Ejecutando: ${runningTool}`;
                } else {
                    currentTool.textContent = `Progreso: ${completed}/${this.tools.length} herramientas`;
                }
            }
        }
    }
    
    updateVulnerabilities(vulnerabilities) {
        const vulnCard = document.getElementById('vulnerabilitiesCard');
        if (!vulnCard) return;
        
        if (vulnerabilities.length === 0) {
            vulnCard.innerHTML = `
                <div class="text-center text-success">
                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                    <h5>No se detectaron vulnerabilidades</h5>
                    <p class="text-muted">El análisis no encontró vulnerabilidades conocidas en este momento.</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="row">';
        
        // Estadisticas rápidas
        const severityCounts = this.countBySeverity(vulnerabilities);
        
        html += `
            <div class="col-12 mb-3">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="p-3 bg-danger text-white rounded">
                            <h4>${severityCounts.critical || 0}</h4>
                            <small>Críticas</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-warning text-white rounded">
                            <h4>${severityCounts.high || 0}</h4>
                            <small>Altas</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-info text-white rounded">
                            <h4>${severityCounts.medium || 0}</h4>
                            <small>Medias</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-success text-white rounded">
                            <h4>${severityCounts.low || 0}</h4>
                            <small>Bajas</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Lista de vulnerabilidades
        html += '<div class="col-12">';
        vulnerabilities.forEach(vuln => {
            const severityClass = this.getSeverityClass(vuln.severity);
            const severityBadge = this.getSeverityBadge(vuln.severity);
            
            html += `
                <div class="vulnerability-item vulnerability-${vuln.severity} mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                ${severityBadge}
                                ${this.escapeHtml(vuln.name)}
                            </h6>
                            <p class="mb-1 text-muted small">${this.escapeHtml(vuln.description)}</p>
                            <small class="text-muted">
                                <strong>Template:</strong> ${this.escapeHtml(vuln.template_id)}<br>
                                <strong>URL:</strong> ${this.escapeHtml(vuln.matched_at)}
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-bug ${severityClass}"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div></div>';
        
        vulnCard.innerHTML = html;
    }
    
    updateDNSInfo(toolsResults) {
        const dnsInfo = document.getElementById('dnsInfo');
        if (!dnsInfo) return;
        
        const digSummary = toolsResults?.dig_summary;
        
        if (!digSummary || digSummary.status !== 'completed') {
            dnsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Consultando DNS...</p>
                </div>
            `;
            return;
        }
        
        const dnsData = digSummary.output || {};
        let html = '';
        
        Object.entries(dnsData).forEach(([recordType, records]) => {
            html += `
                <div class="mb-3">
                    <h6><strong>${recordType} Records:</strong></h6>
            `;
            
            if (records && records.length > 0 && records[0] !== '') {
                html += '<ul class="list-unstyled ms-3">';
                records.forEach(record => {
                    if (record.trim()) {
                        html += `<li><code>${this.escapeHtml(record)}</code></li>`;
                    }
                });
                html += '</ul>';
            } else {
                html += '<p class="text-muted ms-3">No hay registros</p>';
            }
            
            html += '</div>';
        });
        
        dnsInfo.innerHTML = html || '<p class="text-muted">No se pudo obtener información DNS</p>';
    }
    
    updateWhoisInfo(toolsResults) {
        const whoisInfo = document.getElementById('whoisInfo');
        if (!whoisInfo) return;
        
        const whoisData = toolsResults?.whois;
        
        if (!whoisData || whoisData.status !== 'completed') {
            whoisInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Consultando WHOIS...</p>
                </div>
            `;
            return;
        }
        
        const output = whoisData.output || '';
        if (output.trim()) {
            // Extraer información clave del WHOIS
            const lines = output.split('\n');
            const keyInfo = {};
            
            lines.forEach(line => {
                const lowerLine = line.toLowerCase();
                if (lowerLine.includes('registrar:')) {
                    keyInfo.registrar = line.split(':')[1]?.trim();
                } else if (lowerLine.includes('creation date:') || lowerLine.includes('created:')) {
                    keyInfo.created = line.split(':')[1]?.trim();
                } else if (lowerLine.includes('expiry date:') || lowerLine.includes('expires:')) {
                    keyInfo.expires = line.split(':')[1]?.trim();
                }
            });
            
            let html = '';
            
            if (Object.keys(keyInfo).length > 0) {
                html += '<div class="mb-3">';
                if (keyInfo.registrar) html += `<p><strong>Registrar:</strong> ${this.escapeHtml(keyInfo.registrar)}</p>`;
                if (keyInfo.created) html += `<p><strong>Creado:</strong> ${this.escapeHtml(keyInfo.created)}</p>`;
                if (keyInfo.expires) html += `<p><strong>Expira:</strong> ${this.escapeHtml(keyInfo.expires)}</p>`;
                html += '</div>';
            }
            
            html += `<details><summary>Ver información completa</summary><pre class="mt-2">${this.escapeHtml(output)}</pre></details>`;
            
            whoisInfo.innerHTML = html;
        } else {
            whoisInfo.innerHTML = '<p class="text-muted">No se pudo obtener información WHOIS</p>';
        }
    }
    
    updateSubdomains(toolsResults) {
        const subdomainsInfo = document.getElementById('subdomainsInfo');
        if (!subdomainsInfo) return;
        
        const assetfinderData = toolsResults?.assetfinder;
        
        if (!assetfinderData || assetfinderData.status !== 'completed') {
            subdomainsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Enumerando subdominios...</p>
                </div>
            `;
            return;
        }
        
        const output = assetfinderData.output || '';
        if (output.trim()) {
            const subdomains = output.split('\n').filter(line => line.trim()).slice(0, 20); // Primeros 20
            
            let html = `<p class="mb-2"><strong>Encontrados:</strong> ${subdomains.length} subdominios</p>`;
            html += '<ul class="list-unstyled">';
            
            subdomains.forEach(subdomain => {
                if (subdomain.trim()) {
                    html += `<li><i class="fas fa-globe text-info me-2"></i><code>${this.escapeHtml(subdomain.trim())}</code></li>`;
                }
            });
            
            html += '</ul>';
            subdomainsInfo.innerHTML = html;
        } else {
            subdomainsInfo.innerHTML = '<p class="text-muted">No se encontraron subdominios</p>';
        }
    }
    
    updateEmails(toolsResults) {
        const emailsInfo = document.getElementById('emailsInfo');
        if (!emailsInfo) return;
        
        const harvesterData = toolsResults?.theharvester;
        
        if (!harvesterData || harvesterData.status !== 'completed') {
            emailsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Recolectando emails...</p>
                </div>
            `;
            return;
        }
        
        // Primero verificar si hay emails en los resultados procesados
        const emailsFound = harvesterData.emails_found || [];
        const output = harvesterData.output || '';
        
        let emails = [];
        
        // Si hay emails procesados, usarlos
        if (emailsFound && emailsFound.length > 0) {
            emails = emailsFound;
        } else if (output.trim()) {
            // Si no, extraer emails del output usando regex
            const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
            emails = [...new Set(output.match(emailRegex) || [])];
        }
        
        if (emails.length > 0) {
            let html = `<p class="mb-2"><strong>Encontrados:</strong> ${emails.length} emails</p>`;
            html += '<ul class="list-unstyled">';
            
            emails.slice(0, 15).forEach(email => { // Primeros 15
                html += `<li><i class="fas fa-envelope text-success me-2"></i><code>${this.escapeHtml(email)}</code></li>`;
            });
            
            if (emails.length > 15) {
                html += `<li class="text-muted">... y ${emails.length - 15} más</li>`;
            }
            
            html += '</ul>';
            emailsInfo.innerHTML = html;
        } else {
            emailsInfo.innerHTML = '<p class="text-muted">No se encontraron emails públicos</p>';
        }
    }
    
    updateRecommendations(recommendations) {
        const recommendationsCard = document.getElementById('recommendationsCard');
        if (!recommendationsCard) return;
        
        if (recommendations.length === 0) {
            recommendationsCard.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-lightbulb fa-2x mb-3"></i>
                    <p>Generando recomendaciones personalizadas...</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="list-group list-group-flush">';
        
        recommendations.forEach(rec => {
            const iconClass = this.getRecommendationIcon(rec.type);
            const badgeClass = this.getRecommendationBadge(rec.type);
            
            html += `
                <div class="list-group-item border-0 px-0">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start">
                                <p class="mb-0">${this.escapeHtml(rec.message)}</p>
                                <span class="${badgeClass} ms-2">${rec.type.toUpperCase()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        recommendationsCard.innerHTML = html;
    }
    
    onScanCompleted(data) {
        const startBtn = document.getElementById('startScanBtn');
        const downloadBtn = document.getElementById('downloadReportBtn');
        const currentTool = document.getElementById('currentTool');
        
        // Rehabilitar botón de escaneo
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-search"></i> Iniciar Análisis Completo';
        }
        
        // Mostrar botón de descarga
        if (downloadBtn && (data.status === 'completed' || data.status === 'completed_with_errors')) {
            downloadBtn.style.display = 'inline-block';
            downloadBtn.classList.add('fade-in');
        }
        
        // Mensaje final
        if (currentTool) {
            if (data.status === 'completed') {
                currentTool.innerHTML = '<i class="fas fa-check text-success"></i> Análisis completado exitosamente';
                this.showAlert('Análisis completado exitosamente', 'success');
            } else if (data.status === 'completed_with_errors') {
                currentTool.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Análisis completado con algunos errores';
                this.showAlert('Análisis completado con algunos errores en las herramientas', 'warning');
            } else {
                currentTool.innerHTML = '<i class="fas fa-times text-danger"></i> Error en el análisis';
                this.showAlert('Error crítico en el análisis', 'danger');
            }
        }
    }
    
    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    
    async downloadReport() {
        if (!this.currentScanId) {
            this.showAlert('No hay reporte disponible para descargar', 'warning');
            return;
        }
        
        try {
            const response = await fetch(`/report/${this.currentScanId}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Error al generar el reporte');
            }
            
            // Descargar archivo
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security_report_${this.currentScanId}.html`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.showAlert('Reporte descargado exitosamente', 'success');
            
        } catch (error) {
            console.error('Error downloading report:', error);
            this.showAlert('Error al descargar el reporte: ' + error.message, 'danger');
        }
    }
    
    showInitialMessage() {
        const initialMessage = document.getElementById('initialMessage');
        const resultsSection = document.getElementById('resultsSection');
        
        if (initialMessage) initialMessage.style.display = 'block';
        if (resultsSection) resultsSection.style.display = 'none';
    }
    
    showAlert(message, type = 'info') {
        // Crear alerta temporal
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'danger' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                }"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insertar alerta al inicio del container
        const container = document.querySelector('.container-fluid');
        if (container) {
            const alertDiv = document.createElement('div');
            alertDiv.innerHTML = alertHtml;
            container.insertBefore(alertDiv.firstElementChild, container.firstChild);
            
            // Auto-remover después de 5 segundos
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 300);
                }
            }, 5000);
        }
    }
    
    // Funciones utilitarias
    countBySeverity(vulnerabilities) {
        return vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
            return acc;
        }, {});
    }
    
    getSeverityClass(severity) {
        const classes = {
            'critical': 'text-danger',
            'high': 'text-warning',
            'medium': 'text-info',
            'low': 'text-success'
        };
        return classes[severity] || 'text-secondary';
    }
    
    getSeverityBadge(severity) {
        const badges = {
            'critical': '<span class="badge bg-danger">CRÍTICA</span>',
            'high': '<span class="badge bg-warning">ALTA</span>',
            'medium': '<span class="badge bg-info">MEDIA</span>',
            'low': '<span class="badge bg-success">BAJA</span>'
        };
        return badges[severity] || `<span class="badge bg-secondary">${severity.toUpperCase()}</span>`;
    }
    
    getRecommendationIcon(type) {
        const icons = {
            'critical': 'fas fa-exclamation-circle text-danger',
            'warning': 'fas fa-exclamation-triangle text-warning',
            'info': 'fas fa-info-circle text-info'
        };
        return icons[type] || 'fas fa-lightbulb text-primary';
    }
    
    getRecommendationBadge(type) {
        const badges = {
            'critical': 'badge bg-danger',
            'warning': 'badge bg-warning',
            'info': 'badge bg-info'
        };
        return badges[type] || 'badge bg-primary';
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Inicializar aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.securityScannerApp = new SecurityScannerApp();
});

