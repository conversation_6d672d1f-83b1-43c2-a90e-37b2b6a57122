/* Security Scanner Web Interface - Custom CSS */

/* Variables CSS */
:root {
    --primary-color: #3498db;
    --danger-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
}

/* Estilos generales */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navegación */
.navbar {
    box-shadow: var(--shadow);
}

/* Tarjetas */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
    font-weight: 600;
}

/* Iconos de herramientas */
.tool-icon {
    padding: 10px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.tool-icon:hover {
    background-color: var(--light-color);
    transform: scale(1.05);
}

.tool-icon i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* Formularios */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Botones */
.btn {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

/* Barra de progreso */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    border-radius: 10px;
}

/* Alertas */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

/* Badges de severidad */
.severity-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.severity-critical {
    background-color: #dc3545;
    color: white;
}

.severity-high {
    background-color: #fd7e14;
    color: white;
}

.severity-medium {
    background-color: #ffc107;
    color: #212529;
}

.severity-low {
    background-color: #28a745;
    color: white;
}

/* Características de la página principal */
.feature-item {
    text-align: center;
    padding: 20px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background-color: white;
    box-shadow: var(--shadow);
    transform: translateY(-3px);
}

/* Spinner personalizado */
.custom-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Lista de vulnerabilidades */
.vulnerability-item {
    border-left: 4px solid;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    background-color: white;
    transition: all 0.3s ease;
}

.vulnerability-item:hover {
    box-shadow: var(--shadow);
    transform: translateX(3px);
}

.vulnerability-critical {
    border-left-color: var(--danger-color);
}

.vulnerability-high {
    border-left-color: var(--warning-color);
}

.vulnerability-medium {
    border-left-color: var(--info-color);
}

.vulnerability-low {
    border-left-color: var(--success-color);
}

/* Pre y código */
pre {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: var(--border-radius);
    padding: 20px;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    border: 1px solid #4a5568;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* Animaciones */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Estados de loading */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 10;
}

/* Tabla responsive */
.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

/* Footer */
footer {
    margin-top: 50px;
    background: linear-gradient(135deg, var(--dark-color), #34495e);
}

/* Responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .feature-item {
        margin-bottom: 20px;
    }
    
    .tool-icon {
        margin-bottom: 10px;
    }
}

/* Tooltip personalizado */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: var(--border-radius);
}

/* Estados de botones */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Efectos de hover en iconos */
.fas, .far, .fab {
    transition: all 0.3s ease;
}

.card-header .fas:hover,
.btn .fas:hover {
    transform: scale(1.1);
}

/* Estilos para números de línea en código */
.line-numbers {
    counter-reset: linenumber;
}

.line-numbers pre {
    counter-increment: linenumber;
}

.line-numbers pre::before {
    content: counter(linenumber);
    color: #6c757d;
    margin-right: 1rem;
    display: inline-block;
    width: 2rem;
    text-align: right;
}

