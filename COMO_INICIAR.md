# 🚀 CÓMO INICIAR TU SECURITY SCANNER

## 🔴 OPCIÓN 1: Inici<PERSON> (Recomendado)

```bash
cd /Users/<USER>/security_scanner_web
./start.sh
```

## 🔵 OPCIÓN 2: Inicio Manual

```bash
cd /Users/<USER>/security_scanner_web
source venv/bin/activate
python app.py
```

## 🌐 Acceder a la Aplicación

Una vez iniciada, abre tu navegador y ve a:
**http://localhost:6000**

## ⚙️ Cómo Usar

1. **Ingresa un dominio** en el campo de texto (ejemplo: `example.com`)
2. **Haz clic en "Iniciar Análisis Completo"**
3. **Observa el progreso** en tiempo real
4. **Descarga el reporte** cuando termine

## ⏹️ Detener la Aplicación

Presiona **Ctrl+C** en la terminal donde está ejecutándose

## 🔧 Si hay problemas

### Puerto ocupado:
```bash
# El puerto se cambiará automáticamente a 8080
# Si aún hay conflictos, edita app.py y cambia el puerto
```

### Herramientas faltantes:
```bash
# Instalar herramientas con Homebrew
brew install nuclei amass theharvester
```

### Dependencias de Python:
```bash
# Reinstalar dependencias
cd /Users/<USER>/security_scanner_web
source venv/bin/activate
pip install -r requirements.txt
```

---

🎉 **¡Tu herramienta está lista para analizar sitios web!**

