#!/bin/bash
# Script de inicio rápido para Security Scanner

echo "🚀 Security Scanner - Inicio Rápido"
echo "==================================="

# Verificar que estamos en el directorio correcto
if [ ! -f "app.py" ]; then
    echo "❌ Error: app.py no encontrado. Ejecuta desde el directorio del proyecto."
    exit 1
fi

# Activar entorno virtual si existe
if [ -d "venv" ]; then
    echo "📦 Activando entorno virtual..."
    source venv/bin/activate
fi

# Verificar herramientas rápidamente
echo "🔍 Verificación rápida de herramientas..."

tools=("nuclei" "theharvester" "assetfinder" "dig" "whois")
tools_ok=0

for tool in "${tools[@]}"; do
    if command -v "$tool" >/dev/null 2>&1; then
        echo "  ✅ $tool: OK"
        ((tools_ok++))
    else
        echo "  ❌ $tool: No encontrado"
    fi
done

echo "📊 Herramientas disponibles: $tools_ok/${#tools[@]}"

if [ $tools_ok -lt 3 ]; then
    echo "⚠️  Pocas herramientas disponibles. Ejecuta: python3 fix_tools.py"
fi

echo ""
echo "🌐 Iniciando Security Scanner Web Interface..."
echo "   URL: http://localhost:3333"
echo "   Presiona Ctrl+C para detener"
echo ""

# Iniciar la aplicación
python3 app.py

