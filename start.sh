#!/bin/bash

# Security Scanner Web Interface - Script de inicio

echo "="*60
echo "Security Scanner Web Interface"
echo "="*60
echo

# Verificar si Python 3 está instalado
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 no está instalado"
    echo "Por favor instala Python 3 y vuelve a intentar"
    exit 1
fi

# Verificar si pip está instalado
if ! command -v pip3 &> /dev/null; then
    echo "❌ Error: pip3 no está instalado"
    echo "Por favor instala pip3 y vuelve a intentar"
    exit 1
fi

# Crear entorno virtual si no existe
if [ ! -d "venv" ]; then
    echo "🔧 Creando entorno virtual..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ Error: No se pudo crear el entorno virtual"
        exit 1
    fi
fi

# Activar entorno virtual
echo "🔧 Activando entorno virtual..."
source venv/bin/activate

# Instalar dependencias si es necesario
if [ ! -f "venv/.dependencies_installed" ]; then
    echo "📦 Instalando dependencias de Python..."
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        touch venv/.dependencies_installed
        echo "✅ Dependencias instaladas correctamente"
    else
        echo "❌ Error: No se pudieron instalar las dependencias"
        exit 1
    fi
else
    echo "✅ Dependencias ya instaladas"
fi

# Verificar herramientas de seguridad
echo
echo "🔍 Verificando herramientas de seguridad disponibles..."

tools_available=0
total_tools=5

if command -v nuclei &> /dev/null; then
    echo "✅ Nuclei: $(nuclei -version 2>/dev/null | head -1)"
    ((tools_available++))
else
    echo "❌ Nuclei: No instalado"
    echo "   Instalar con: brew install nuclei"
fi

if command -v amass &> /dev/null; then
    echo "✅ Amass: Instalado"
    ((tools_available++))
else
    echo "❌ Amass: No instalado"
    echo "   Instalar con: brew install amass"
fi

if command -v theharvester &> /dev/null; then
    echo "✅ theHarvester: Instalado"
    ((tools_available++))
else
    echo "❌ theHarvester: No instalado"
    echo "   Instalar con: brew install theharvester"
fi

if command -v dig &> /dev/null; then
    echo "✅ dig: Instalado"
    ((tools_available++))
else
    echo "❌ dig: No instalado (normalmente viene preinstalado)"
fi

if command -v whois &> /dev/null; then
    echo "✅ whois: Instalado"
    ((tools_available++))
else
    echo "❌ whois: No instalado (normalmente viene preinstalado)"
fi

echo
echo "📊 Herramientas disponibles: $tools_available/$total_tools"

if [ $tools_available -lt 3 ]; then
    echo "⚠️  Advertencia: Pocas herramientas disponibles. La funcionalidad será limitada."
    echo "   Se recomienda instalar al menos Nuclei, Amass y theHarvester"
fi

# Crear directorio de reportes si no existe
if [ ! -d "reports" ]; then
    mkdir reports
    echo "📁 Directorio 'reports' creado"
fi

echo
echo "🚀 Iniciando Security Scanner Web Interface..."
echo
echo "📍 La aplicación estará disponible en: http://localhost:8080"
echo "⏹️  Para detener la aplicación, presiona Ctrl+C"
echo
echo "="*60
echo

# Iniciar la aplicación
python app.py

