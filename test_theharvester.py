#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba específico para theHarvester
"""

import subprocess
import sys

def test_theharvester():
    target = "google.com"
    command = f"theharvester -d {target} -l 20 -b crtsh,hackertarget,duckduckgo,bing"
    
    print(f"🔍 Probando theHarvester con: {command}")
    print("" + "-"*60)
    
    try:
        process = subprocess.Popen(
            command.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            universal_newlines=True
        )
        
        stdout, stderr = process.communicate(timeout=60)
        
        print(f"📊 Código de retorno: {process.returncode}")
        print(f"📝 Salida stdout:")
        print(stdout)
        
        if stderr:
            print(f"⚠️  Salida stderr:")
            print(stderr)
        
        if process.returncode == 0:
            print("✅ theHarvester funcionó correctamente")
            
            # Extraer emails y hosts
            lines = stdout.split('\n')
            emails = []
            hosts = []
            
            in_hosts_section = False
            for line in lines:
                if "emails found" in line.lower():
                    continue
                elif "hosts found" in line.lower():
                    in_hosts_section = True
                    continue
                elif line.startswith("---"):
                    continue
                elif in_hosts_section and line.strip():
                    if not line.startswith("[*]") and not line.startswith("*"):
                        hosts.append(line.strip())
                elif "@" in line and "." in line:
                    emails.append(line.strip())
            
            print(f"\n📧 Emails encontrados: {len(emails)}")
            for email in emails[:5]:  # Mostrar primeros 5
                print(f"  - {email}")
            
            print(f"\n🌐 Hosts encontrados: {len(hosts)}")
            for host in hosts[:10]:  # Mostrar primeros 10
                print(f"  - {host}")
                
        else:
            print(f"❌ theHarvester falló con código {process.returncode}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout - theHarvester tardó más de 60 segundos")
    except FileNotFoundError:
        print("❌ theHarvester no encontrado. ¿Está instalado?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_theharvester()

