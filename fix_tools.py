#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar, reparar y actualizar herramientas de seguridad
"""

import subprocess
import sys
import os

def run_command(command):
    """Ejecuta un comando y retorna la salida"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 124, "", "Timeout"
    except Exception as e:
        return 1, "", str(e)

def check_tool(tool_name, command):
    """Verifica si una herramienta está disponible y funcionando"""
    print(f"\n🔍 Verificando {tool_name}...")
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print(f"✅ {tool_name} está funcionando correctamente")
        return True
    else:
        print(f"❌ {tool_name} tiene problemas:")
        if stderr:
            print(f"   Error: {stderr[:200]}")
        return False

def fix_nuclei():
    """Repara y actualiza Nuclei"""
    print("\n🔧 Reparando Nuclei...")
    
    # Actualizar templates
    print("📦 Actualizando templates de Nuclei...")
    returncode, stdout, stderr = run_command("nuclei -ut")
    if returncode == 0:
        print("✅ Templates actualizados correctamente")
    else:
        print(f"⚠️  Advertencia actualizando templates: {stderr[:100]}")
    
    # Verificar configuración
    print("⚙️  Verificando configuración de Nuclei...")
    returncode, stdout, stderr = run_command("nuclei -target https://httpbin.org/status/200 -tags info -silent -timeout 5")
    if returncode == 0:
        print("✅ Nuclei configurado correctamente")
        return True
    else:
        print(f"❌ Nuclei aún tiene problemas: {stderr[:100]}")
        return False

def fix_theharvester():
    """Repara theHarvester"""
    print("\n🔧 Reparando theHarvester...")
    
    # Verificar fuentes disponibles
    print("📋 Verificando fuentes disponibles...")
    returncode, stdout, stderr = run_command("theharvester --help")
    if "baidu, bevigil, bing" in stdout:
        print("✅ theHarvester tiene fuentes actualizadas")
    
    # Crear configuración básica si no existe
    config_dir = os.path.expanduser("~/.theHarvester")
    if not os.path.exists(config_dir):
        print(f"📁 Creando directorio de configuración: {config_dir}")
        os.makedirs(config_dir, exist_ok=True)
    
    # Verificar con una prueba rápida
    print("🧪 Probando theHarvester con dominio de prueba...")
    returncode, stdout, stderr = run_command("theharvester -d google.com -l 5 -b bing")
    if returncode == 0 and "google.com" in stdout:
        print("✅ theHarvester funciona correctamente")
        return True
    else:
        print(f"❌ theHarvester tiene problemas: {stderr[:100]}")
        return False

def fix_assetfinder():
    """Verifica Assetfinder"""
    print("\n🔧 Verificando Assetfinder...")
    
    # Verificar que está en el PATH
    returncode, stdout, stderr = run_command("which assetfinder")
    if returncode != 0:
        print("❌ Assetfinder no está en el PATH")
        print("💡 Intenta: export PATH=$PATH:$HOME/go/bin")
        return False
    
    # Probar funcionamiento
    returncode, stdout, stderr = run_command("assetfinder --subs-only google.com | head -3")
    if returncode == 0 and "google.com" in stdout:
        print("✅ Assetfinder funciona correctamente")
        return True
    else:
        print(f"❌ Assetfinder tiene problemas: {stderr[:100]}")
        return False

def main():
    print("🛠️  Security Scanner Tools Repair Script")
    print("=" * 50)
    
    tools_status = {}
    
    # Verificar herramientas básicas
    print("\n📋 VERIFICACIÓN INICIAL")
    tools_status['whois'] = check_tool('whois', 'whois --version')
    tools_status['dig'] = check_tool('dig', 'dig -v')
    
    # Verificar y reparar herramientas principales
    print("\n🔧 REPARACIÓN DE HERRAMIENTAS")
    
    # Nuclei
    if not check_tool('nuclei', 'nuclei -version'):
        print("❌ Nuclei no está instalado o no funciona")
        print("💡 Instala con: brew install nuclei")
        tools_status['nuclei'] = False
    else:
        tools_status['nuclei'] = fix_nuclei()
    
    # theHarvester
    if not check_tool('theharvester', 'theharvester --help'):
        print("❌ theHarvester no está instalado")
        print("💡 Instala con: brew install theharvester")
        tools_status['theharvester'] = False
    else:
        tools_status['theharvester'] = fix_theharvester()
    
    # Assetfinder
    tools_status['assetfinder'] = fix_assetfinder()
    
    # Resumen final
    print("\n📊 RESUMEN FINAL")
    print("=" * 30)
    for tool, status in tools_status.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {tool}: {'OK' if status else 'PROBLEMA'}")
    
    # Recomendaciones
    print("\n💡 RECOMENDACIONES:")
    if not tools_status.get('nuclei', False):
        print("   • Nuclei: Actualiza templates con 'nuclei -ut'")
    if not tools_status.get('theharvester', False):
        print("   • theHarvester: Usa fuentes válidas como 'bing,crtsh,hackertarget'")
    if not tools_status.get('assetfinder', False):
        print("   • Assetfinder: Verifica que $HOME/go/bin esté en tu PATH")
    
    print("\n🎯 Las herramientas han sido verificadas y reparadas.")
    print("   Prueba la aplicación web ahora.")

if __name__ == "__main__":
    main()

