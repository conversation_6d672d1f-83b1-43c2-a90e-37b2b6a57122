<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte de Seguridad - {{ results.target }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .severity-critical { border-left: 5px solid #dc3545; }
        .severity-high { border-left: 5px solid #fd7e14; }
        .severity-medium { border-left: 5px solid #ffc107; }
        .severity-low { border-left: 5px solid #28a745; }
        .severity-info { border-left: 5px solid #17a2b8; }
        .report-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .vulnerability-card { transition: all 0.3s ease; }
        .vulnerability-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .summary-stat { text-align: center; padding: 20px; }
        .summary-stat i { font-size: 2.5rem; margin-bottom: 10px; }
        .tool-section { border-radius: 10px; margin-bottom: 30px; }
        .section-header { background: #f8f9fa; padding: 15px; border-radius: 10px 10px 0 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 0.9rem; }
        .recommendation-item { border-left: 4px solid #007bff; padding-left: 15px; margin-bottom: 15px; }
        .recommendation-critical { border-left-color: #dc3545; }
        .recommendation-warning { border-left-color: #ffc107; }
        .recommendation-info { border-left-color: #17a2b8; }
    </style>
</head>
<body>
    <!-- Header del reporte -->
    <div class="report-header text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-shield-alt"></i> Reporte de Seguridad</h1>
                    <h3 class="mb-0">{{ results.target }}</h3>
                </div>
                <div class="col-md-4 text-end">
                    <p class="mb-1"><strong>Fecha:</strong> {{ results.start_time.split('T')[0] }}</p>
                    <p class="mb-1"><strong>Hora:</strong> {{ results.start_time.split('T')[1].split('.')[0] }}</p>
                    <p class="mb-0"><strong>ID:</strong> {{ results.scan_id }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <!-- Resumen ejecutivo -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-chart-bar"></i> Resumen Ejecutivo</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="summary-stat">
                                    <i class="fas fa-bug text-danger"></i>
                                    <h3 class="text-danger">{{ results.vulnerabilities|length }}</h3>
                                    <p class="mb-0">Vulnerabilidades</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-stat">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                    <h3 class="text-warning">{{ results.vulnerabilities|selectattr('severity', 'equalto', 'high')|list|length }}</h3>
                                    <p class="mb-0">Alta Severidad</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-stat">
                                    <i class="fas fa-tools text-info"></i>
                                    <h3 class="text-info">{{ results.tools_results|length }}</h3>
                                    <p class="mb-0">Herramientas</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-stat">
                                    <i class="fas fa-lightbulb text-success"></i>
                                    <h3 class="text-success">{{ results.recommendations|length }}</h3>
                                    <p class="mb-0">Recomendaciones</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vulnerabilidades detectadas -->
        {% if results.vulnerabilities %}
        <div class="card shadow mb-4">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Vulnerabilidades Detectadas</h4>
            </div>
            <div class="card-body">
                {% for vuln in results.vulnerabilities %}
                <div class="card vulnerability-card mb-3 severity-{{ vuln.severity }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="card-title">
                                    {% if vuln.severity == 'critical' %}
                                        <span class="badge bg-danger">CRÍTICA</span>
                                    {% elif vuln.severity == 'high' %}
                                        <span class="badge bg-warning">ALTA</span>
                                    {% elif vuln.severity == 'medium' %}
                                        <span class="badge bg-info">MEDIA</span>
                                    {% elif vuln.severity == 'low' %}
                                        <span class="badge bg-success">BAJA</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ vuln.severity.upper() }}</span>
                                    {% endif %}
                                    {{ vuln.name }}
                                </h5>
                                <p class="card-text">{{ vuln.description }}</p>
                                <small class="text-muted">
                                    <strong>Template:</strong> {{ vuln.template_id }}<br>
                                    <strong>URL:</strong> {{ vuln.matched_at }}
                                </small>
                            </div>
                            <div class="col-md-4">
                                {% if vuln.reference %}
                                <h6>Referencias:</h6>
                                <ul class="list-unstyled">
                                    {% for ref in vuln.reference %}
                                    <li><a href="{{ ref }}" target="_blank" class="text-primary"><i class="fas fa-external-link-alt"></i> {{ ref }}</a></li>
                                    {% endfor %}
                                </ul>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Información DNS -->
        {% if results.tools_results.dig_summary %}
        <div class="card shadow mb-4 tool-section">
            <div class="section-header">
                <h4 class="mb-0"><i class="fas fa-globe text-primary"></i> Información DNS</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for record_type, records in results.tools_results.dig_summary.output.items() %}
                    <div class="col-md-6 mb-3">
                        <h6><strong>{{ record_type }} Records:</strong></h6>
                        {% if records %}
                        <ul class="list-group list-group-flush">
                            {% for record in records %}
                            <li class="list-group-item px-0">{{ record }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <p class="text-muted">No se encontraron registros {{ record_type }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Información WHOIS -->
        {% if results.tools_results.whois %}
        <div class="card shadow mb-4 tool-section">
            <div class="section-header">
                <h4 class="mb-0"><i class="fas fa-id-card text-secondary"></i> Información WHOIS</h4>
            </div>
            <div class="card-body">
                <pre>{{ results.tools_results.whois.output }}</pre>
            </div>
        </div>
        {% endif %}

        <!-- Subdominios (Amass) -->
        {% if results.tools_results.amass %}
        <div class="card shadow mb-4 tool-section">
            <div class="section-header">
                <h4 class="mb-0"><i class="fas fa-sitemap text-info"></i> Subdominios Encontrados (Amass)</h4>
            </div>
            <div class="card-body">
                {% if results.tools_results.amass.output %}
                <pre>{{ results.tools_results.amass.output }}</pre>
                {% else %}
                <p class="text-muted">No se encontraron subdominios o la herramienta no está disponible.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Emails (theHarvester) -->
        {% if results.tools_results.theharvester %}
        <div class="card shadow mb-4 tool-section">
            <div class="section-header">
                <h4 class="mb-0"><i class="fas fa-envelope text-success"></i> Emails Encontrados (theHarvester)</h4>
            </div>
            <div class="card-body">
                {% if results.tools_results.theharvester.output %}
                <pre>{{ results.tools_results.theharvester.output }}</pre>
                {% else %}
                <p class="text-muted">No se encontraron emails o la herramienta no está disponible.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Recomendaciones -->
        {% if results.recommendations %}
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-lightbulb"></i> Recomendaciones de Seguridad</h4>
            </div>
            <div class="card-body">
                {% for rec in results.recommendations %}
                <div class="recommendation-item recommendation-{{ rec.type }}">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            {% if rec.type == 'critical' %}
                                <i class="fas fa-exclamation-circle text-danger"></i>
                            {% elif rec.type == 'warning' %}
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            {% else %}
                                <i class="fas fa-info-circle text-info"></i>
                            {% endif %}
                        </div>
                        <div>
                            <p class="mb-0">{{ rec.message }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Detalles técnicos -->
        <div class="card shadow mb-4">
            <div class="card-header bg-dark text-white">
                <h4 class="mb-0"><i class="fas fa-cogs"></i> Detalles Técnicos del Análisis</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><strong>Herramientas Utilizadas:</strong></h6>
                        <ul class="list-group list-group-flush">
                            {% for tool_name, tool_result in results.tools_results.items() %}
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                {{ tool_name }}
                                {% if tool_result.status == 'completed' %}
                                    <span class="badge bg-success">Completado</span>
                                {% elif tool_result.status == 'error' %}
                                    <span class="badge bg-danger">Error</span>
                                {% elif tool_result.status == 'timeout' %}
                                    <span class="badge bg-warning">Timeout</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ tool_result.status }}</span>
                                {% endif %}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><strong>Información del Análisis:</strong></h6>
                        <table class="table table-sm">
                            <tr><td><strong>Target:</strong></td><td>{{ results.target }}</td></tr>
                            <tr><td><strong>Inicio:</strong></td><td>{{ results.start_time.split('T')[0] }} {{ results.start_time.split('T')[1].split('.')[0] }}</td></tr>
                            {% if results.end_time %}
                            <tr><td><strong>Fin:</strong></td><td>{{ results.end_time.split('T')[0] }} {{ results.end_time.split('T')[1].split('.')[0] }}</td></tr>
                            {% endif %}
                            <tr><td><strong>Estado:</strong></td><td>
                                {% if results.status == 'completed' %}
                                    <span class="badge bg-success">Completado</span>
                                {% elif results.status == 'error' %}
                                    <span class="badge bg-danger">Error</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ results.status }}</span>
                                {% endif %}
                            </td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">Reporte generado por Security Scanner Pro | {{ results.start_time.split('T')[0] }}</p>
            <small class="text-muted">Este reporte contiene información sensible. Distribuir únicamente a personal autorizado.</small>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>

