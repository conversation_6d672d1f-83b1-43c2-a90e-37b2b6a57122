#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que las herramientas funcionan correctamente
"""

import subprocess
import sys

def test_tool(tool_name, command, test_target="google.com"):
    """Prueba una herramienta de seguridad"""
    print(f"🔧 Probando {tool_name}...")
    
    try:
        # Reemplazar {target} en el comando
        cmd = command.format(target=test_target)
        
        # Ejecutar comando con timeout
        result = subprocess.run(
            cmd.split(),
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print(f"✅ {tool_name}: Funcionando correctamente")
            return True
        else:
            print(f"⚠️  {tool_name}: Retornó código {result.returncode}")
            if result.stderr:
                print(f"   Error: {result.stderr[:100]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏱️  {tool_name}: Timeout (normal para algunas herramientas)")
        return True
    except FileNotFoundError:
        print(f"❌ {tool_name}: No encontrado")
        return False
    except Exception as e:
        print(f"❌ {tool_name}: Error - {str(e)}")
        return False

def main():
    print("🧪 Ejecutando pruebas de herramientas de seguridad...")
    print("" + "="*50)
    
    # Definir herramientas a probar
    tools = [
        ("whois", "whois {target}"),
        ("dig", "dig A {target} +short"),
        ("nuclei", "nuclei -version"),  # Solo verificar versión
        ("amass", "amass -version"),   # Solo verificar versión  
        ("theharvester", "theharvester --help")  # Solo verificar help
    ]
    
    successful = 0
    total = len(tools)
    
    for tool_name, command in tools:
        if test_tool(tool_name, command):
            successful += 1
        print()
    
    print("📊 Resumen de pruebas:")
    print(f"   ✅ Exitosas: {successful}/{total}")
    print(f"   ❌ Fallidas: {total - successful}/{total}")
    
    if successful >= 3:
        print("\n🎉 ¡Suficientes herramientas funcionando! La aplicación debería funcionar bien.")
    else:
        print("\n⚠️  Pocas herramientas funcionando. Considera instalar las faltantes.")
    
    print("\n🚀 Para iniciar la aplicación, ejecuta:")
    print("   ./start.sh")
    print("   o")
    print("   python app.py")

if __name__ == "__main__":
    main()

