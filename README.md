# Security Scanner Web Interface

Herramienta web interactiva para análisis de seguridad que integra múltiples herramientas de reconocimiento y detección de vulnerabilidades.

## 🛠️ Herramientas Integradas

- **Nuclei 3.4.4** - Detección de vulnerabilidades
- **theHarvester** - Recolección OSINT de emails y subdominios
- **Assetfinder** - Enumeración rápida de subdominios
- **dig** - Consultas DNS
- **whois** - Información de registro de dominio

## ✨ Características

- **Interfaz Web Moderna**: Interfaz responsiva construida con Bootstrap 5
- **Análisis en Tiempo Real**: Visualización del progreso y resultados en vivo
- **Reportes HTML**: Generación automática de reportes detallados y profesionales
- **Múl<PERSON>les Herramientas**: Integración de 5 herramientas de seguridad populares
- **Recomendaciones**: Consejos automáticos basados en los resultados del análisis
- **Validación de Entrada**: Verificación automática de formato de dominios

## 🚀 Instalación

### Prerrequisitos

Asegúrate de tener instaladas las siguientes herramientas en tu sistema:

```bash
# macOS con Homebrew
brew install nuclei
go install github.com/tomnomnom/assetfinder@latest
brew install theharvester

# Las herramientas dig y whois normalmente vienen preinstaladas en macOS/Linux
```

### Instalación de la aplicación

1. **Clonar o descargar el proyecto**:
```bash
cd security_scanner_web/
```

2. **Crear entorno virtual de Python**:
```bash
python3 -m venv venv
source venv/bin/activate  # En macOS/Linux
```

3. **Instalar dependencias de Python**:
```bash
pip install -r requirements.txt
```

4. **Verificar herramientas disponibles**:
```bash
# Verificar que las herramientas estén disponibles
nuclei -version
assetfinder --help
theharvester --help
dig -v
whois --version
```

## 🎯 Uso

### Iniciar la aplicación

```bash
python app.py
```

La aplicación estará disponible en: `http://localhost:5000`

### Realizar un análisis

1. **Abrir la interfaz web** en tu navegador
2. **Introducir un dominio** (ejemplo: `example.com`)
3. **Hacer clic en "Iniciar Análisis Completo"**
4. **Observar el progreso** en tiempo real
5. **Descargar el reporte** una vez completado el análisis

## 📊 Resultados del Análisis

La herramienta proporciona:

### Información DNS
- Registros A, AAAA, MX, NS, TXT
- Análisis de configuración DNS

### Información WHOIS
- Datos de registro del dominio
- Fechas de creación y expiración
- Información del registrador

### Vulnerabilidades (Nuclei)
- Detección automática de vulnerabilidades conocidas
- Clasificación por severidad (Crítica, Alta, Media, Baja)
- Referencias y descripciones detalladas

### Reconocimiento OSINT
- **Subdominios** encontrados con Assetfinder
- **Emails** recolectados con theHarvester
- Información de superficie de ataque

### Recomendaciones de Seguridad
- Consejos personalizados basados en los hallazgos
- Mejores prácticas de seguridad
- Acciones prioritarias recomendadas

## 📁 Estructura del Proyecto

```
security_scanner_web/
├── app.py                 # Aplicación Flask principal
├── requirements.txt       # Dependencias de Python
├── README.md             # Este archivo
├── templates/            # Plantillas HTML
│   ├── index.html        # Interfaz principal
│   └── report.html       # Plantilla de reporte
├── static/               # Archivos estáticos
│   ├── css/
│   │   └── custom.css    # Estilos personalizados
│   └── js/
│       └── app.js        # JavaScript de la aplicación
└── reports/              # Reportes generados
```

## ⚙️ Configuración

### Variables de entorno (opcional)

```bash
export FLASK_ENV=development  # Para modo desarrollo
export FLASK_DEBUG=1          # Para habilitar debug
```

### Configuración de herramientas

Puedes personalizar los comandos de las herramientas editando la clase `SecurityScanner` en `app.py`.

## 🔒 Consideraciones de Seguridad

⚠️ **IMPORTANTE**: Esta herramienta está diseñada para uso en entornos de desarrollo y testing.

- **Solo usar en dominios propios** o con autorización explícita
- **No ejecutar en producción** sin configuración de seguridad adicional
- Los reportes pueden contener información sensible
- Limitar el acceso a la interfaz web en redes públicas

## 🐛 Solución de Problemas

### Error: "Herramienta no encontrada"
```bash
# Verificar que las herramientas estén en el PATH
which nuclei
which amass
which theharvester
```

### Error: "Port already in use"
```bash
# Cambiar el puerto en app.py
app.run(debug=True, host='0.0.0.0', port=5001)
```

### Problemas de permisos
```bash
# Asegurar permisos de ejecución
chmod +x app.py
```

## 📈 Características Avanzadas

- **Análisis concurrentes**: Soporta múltiples análisis simultáneos
- **Timeouts configurables**: Control de tiempo límite por herramienta
- **Logging detallado**: Registro de todas las operaciones
- **API REST**: Endpoints para integración programática

## 🔄 API Endpoints

- `POST /scan` - Iniciar nuevo análisis
- `GET /status/{scan_id}` - Obtener estado del análisis
- `GET /report/{scan_id}` - Descargar reporte HTML

## 📝 Licencia

Este proyecto es para fines educativos y de testing. Usar responsablemente.

## 🤝 Contribuciones

Las mejoras y sugerencias son bienvenidas. Asegúrate de:

1. Probar todos los cambios localmente
2. Seguir las mejores prácticas de seguridad
3. Documentar nuevas funcionalidades

---

**⚡ Desarrollado para análisis de seguridad profesional con interfaz moderna y reportes detallados**

