#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Security Scanner Web Interface
Herramienta web interactiva para análisis de seguridad con múltiples herramientas
"""

import os
import json
import uuid
import subprocess
import threading
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
import time
import re

app = Flask(__name__)
app.secret_key = 'security_scanner_2024'

# Configuración
REPORTS_DIR = 'reports'
MAX_CONCURRENT_SCANS = 3
active_scans = {}
scan_results = {}

class SecurityScanner:
    def __init__(self, target, scan_id):
        self.target = target
        self.scan_id = scan_id
        self.results = {
            'target': target,
            'scan_id': scan_id,
            'start_time': datetime.now().isoformat(),
            'status': 'running',
            'tools_results': {},
            'vulnerabilities': [],
            'recommendations': []
        }
        
    def run_command(self, command, tool_name, timeout=30):
        """Ejecuta un comando con captura en tiempo real"""
        try:
            self.results['tools_results'][tool_name] = {
                'status': 'running', 
                'output': '', 
                'errors': '',
                'progress': 'Iniciando...'
            }
            
            print(f"[{tool_name}] Ejecutando: {command}")
            
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            output_lines = []
            error_lines = []
            
            try:
                stdout, stderr = process.communicate(timeout=timeout)
                
                if stdout:
                    output_lines.append(stdout.strip())
                if stderr:
                    error_lines.append(stderr.strip())
                    
                final_output = '\n'.join(filter(None, output_lines))
                final_errors = '\n'.join(filter(None, error_lines))
                
                # Determinar estado final
                if process.returncode == 0:
                    status = 'completed'
                elif process.returncode == 124:  # timeout command
                    status = 'timeout'
                else:
                    status = 'error'
                
                self.results['tools_results'][tool_name] = {
                    'status': status,
                    'output': final_output,
                    'errors': final_errors,
                    'return_code': process.returncode,
                    'progress': f'Completado - {status}'
                }
                
                print(f"[{tool_name}] Terminado con estado: {status}")
                return final_output, final_errors, process.returncode
                
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                self.results['tools_results'][tool_name].update({
                    'status': 'timeout',
                    'errors': f'Timeout después de {timeout} segundos',
                    'progress': 'Timeout'
                })
                print(f"[{tool_name}] Timeout después de {timeout} segundos")
                return '', f'Timeout después de {timeout} segundos', 1
                
        except Exception as e:
            self.results['tools_results'][tool_name].update({
                'status': 'error',
                'errors': str(e),
                'progress': f'Error: {str(e)}'
            })
            print(f"[{tool_name}] Error: {str(e)}")
            return '', str(e), 1
    
    def run_whois(self):
        """Ejecuta análisis WHOIS (optimizado)"""
        command = f"timeout 15 whois {self.target}"
        stdout, stderr, returncode = self.run_command(command, 'whois', timeout=20)
        
        if returncode == 0:
            self.parse_whois_output(stdout)
    
    def run_dig(self):
        """Ejecuta análisis DNS con dig (optimizado para velocidad)"""
        try:
            # Solo los registros más importantes con timeouts cortos
            commands = {
                'A': f"timeout 10 dig A {self.target} +short +time=2 +tries=1",
                'MX': f"timeout 10 dig MX {self.target} +short +time=2 +tries=1",
                'NS': f"timeout 10 dig NS {self.target} +short +time=2 +tries=1"
            }

            dig_results = {}
            all_successful = True

            for record_type, command in commands.items():
                stdout, stderr, returncode = self.run_command(command, f'dig_{record_type}', timeout=15)
                dig_results[record_type] = stdout.strip().split('\n') if stdout.strip() else []
                if returncode != 0:
                    all_successful = False

            # Set the main dig tool status correctly
            self.results['tools_results']['dig'] = {
                'status': 'completed' if all_successful else 'completed_with_errors',
                'output': 'DNS queries completed',
                'errors': '' if all_successful else 'Some DNS queries failed'
            }

            self.results['tools_results']['dig_summary'] = {
                'status': 'completed' if all_successful else 'completed_with_errors',
                'output': dig_results
            }

        except Exception as e:
            self.results['tools_results']['dig'] = {
                'status': 'error',
                'errors': str(e),
                'output': ''
            }
            self.results['tools_results']['dig_summary'] = {
                'status': 'error',
                'errors': str(e),
                'output': {}
            }
    
    def run_theharvester(self):
        """Ejecuta theHarvester (optimizado para emails y velocidad)"""
        # Usar fuentes válidas y funcionales para encontrar emails y hosts
        command = f"timeout 60 theharvester -d {self.target} -l 200 -b bing,yahoo,duckduckgo,crtsh,hackertarget,rapiddns,subdomaincenter -f /tmp/harvester_{self.scan_id}.json"
        stdout, stderr, returncode = self.run_command(command, 'theharvester', timeout=65)
        
        # Extraer emails del output
        if stdout:
            self.parse_theharvester_output(stdout)
        
        # También intentar leer el archivo JSON si existe
        json_file = f"/tmp/harvester_{self.scan_id}.json"
        try:
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    json_data = json.load(f)
                    if 'emails' in json_data:
                        # Actualizar resultado con emails del JSON
                        current_result = self.results['tools_results'].get('theharvester', {})
                        current_result['emails_found'] = json_data['emails']
                        current_result['hosts_found'] = json_data.get('hosts', [])
                os.remove(json_file)  # Limpiar archivo temporal
        except Exception as e:
            print(f"[theharvester] Error leyendo JSON: {e}")
    
    def run_assetfinder(self):
        """Ejecuta Assetfinder para enumeración ultrarrápida de subdominios"""
        # Timeout corto para máxima velocidad
        command = f"timeout 25 assetfinder --subs-only {self.target} | head -50"
        self.run_command(command, 'assetfinder', timeout=30)
    
    def run_nuclei(self):
        """Ejecuta Nuclei para detección de vulnerabilidades"""
        # Configuración balanceada: incluir http:// y https://
        target_with_protocol = self.target if self.target.startswith(('http://', 'https://')) else f"https://{self.target}"
        command = f"timeout 90 nuclei -target {target_with_protocol} -jsonl -silent -no-color -severity critical,high,medium -timeout 10 -retries 2 -c 3 -rl 30 -tags cve,exposure,misconfiguration -nh -duc -ni"
        stdout, stderr, returncode = self.run_command(command, 'nuclei', timeout=95)
        
        # Si https falla, probar con http
        if not stdout and not target_with_protocol.startswith('http://'):
            target_http = f"http://{self.target}"
            print(f"[nuclei] Probando con HTTP: {target_http}")
            command_http = f"timeout 60 nuclei -target {target_http} -jsonl -silent -no-color -severity critical,high,medium -timeout 8 -retries 1 -c 3 -rl 30 -tags cve,exposure -nh -duc -ni"
            stdout, stderr, returncode = self.run_command(command_http, 'nuclei_http', timeout=65)
        
        if stdout:  # Si hay salida, parsear
            self.parse_nuclei_output(stdout)
        elif returncode == 124:
            print(f"[nuclei] Timeout - esto es normal para sitios seguros")
        else:
            print(f"[nuclei] No se encontraron vulnerabilidades")
    
    def parse_whois_output(self, output):
        """Parsea la salida de WHOIS y extrae información relevante"""
        whois_info = {}
        for line in output.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                whois_info[key.strip()] = value.strip()
        
        # Verificar fechas de expiración
        for key in whois_info:
            if 'expir' in key.lower() or 'renew' in key.lower():
                self.check_domain_expiration(whois_info[key])
    
    def parse_theharvester_output(self, output):
        """Parsea la salida de theHarvester y extrae emails"""
        emails = set()
        hosts = set()
        
        # Buscar emails con regex
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        found_emails = re.findall(email_pattern, output)
        emails.update(found_emails)
        
        # Buscar hosts/subdominios
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            # Si la línea contiene el dominio objetivo y parece un subdominio
            if self.target in line and ('.' in line) and not line.startswith('*'):
                # Limpiar la línea
                cleaned = re.sub(r'^[\s*-]+', '', line)
                if cleaned and not cleaned.startswith('[') and not cleaned.startswith('*'):
                    hosts.add(cleaned)
        
        # Actualizar resultados
        if 'theharvester' in self.results['tools_results']:
            self.results['tools_results']['theharvester']['emails_found'] = list(emails)
            self.results['tools_results']['theharvester']['hosts_found'] = list(hosts)
            print(f"[theharvester] Encontrados: {len(emails)} emails, {len(hosts)} hosts")
    
    def parse_nuclei_output(self, output):
        """Parsea la salida JSON de Nuclei"""
        for line in output.strip().split('\n'):
            if line.strip():
                try:
                    vuln = json.loads(line)
                    self.results['vulnerabilities'].append({
                        'severity': vuln.get('info', {}).get('severity', 'unknown'),
                        'name': vuln.get('info', {}).get('name', 'Unknown'),
                        'description': vuln.get('info', {}).get('description', ''),
                        'reference': vuln.get('info', {}).get('reference', []),
                        'matched_at': vuln.get('matched-at', ''),
                        'template_id': vuln.get('template-id', '')
                    })
                except json.JSONDecodeError:
                    continue
    
    def check_domain_expiration(self, date_str):
        """Verifica si el dominio está próximo a expirar"""
        # Implementación básica - se puede mejorar
        if 'month' in date_str.lower() or '30' in date_str:
            self.results['recommendations'].append({
                'type': 'warning',
                'message': 'El dominio podría estar próximo a expirar. Verificar fecha de renovación.'
            })
    
    def generate_recommendations(self):
        """Genera recomendaciones basadas en los resultados"""
        recommendations = []
        
        # Recomendaciones basadas en vulnerabilidades
        if self.results['vulnerabilities']:
            high_severity = [v for v in self.results['vulnerabilities'] if v['severity'] == 'high']
            if high_severity:
                recommendations.append({
                    'type': 'critical',
                    'message': f'Se encontraron {len(high_severity)} vulnerabilidades de alta severidad. Revisar inmediatamente.'
                })
        
        # Recomendaciones generales
        recommendations.extend([
            {
                'type': 'info',
                'message': 'Implementar un WAF (Web Application Firewall) si no está presente.'
            },
            {
                'type': 'info',
                'message': 'Mantener todas las aplicaciones y dependencias actualizadas.'
            },
            {
                'type': 'info',
                'message': 'Realizar análisis de seguridad periódicos.'
            }
        ])
        
        self.results['recommendations'].extend(recommendations)
    
    def run_full_scan(self):
        """Ejecuta el análisis completo y espera a que todas las herramientas terminen"""
        try:
            print(f"Iniciando análisis completo para: {self.target}")
            self.results['status'] = 'running'
            
            # Lista de herramientas en orden de ejecución
            tools_to_run = [
                ('whois', self.run_whois),
                ('dig', self.run_dig), 
                ('theharvester', self.run_theharvester),
                ('assetfinder', self.run_assetfinder),
                ('nuclei', self.run_nuclei)
            ]
            
            # Ejecutar cada herramienta y verificar su estado
            for tool_name, tool_function in tools_to_run:
                print(f"Ejecutando {tool_name}...")
                try:
                    # Marcar como iniciada
                    if tool_name not in self.results['tools_results']:
                        self.results['tools_results'][tool_name] = {'status': 'running'}
                    
                    # Ejecutar la herramienta
                    tool_function()
                    
                    # Verificar que terminó correctamente
                    tool_result = self.results['tools_results'].get(tool_name, {})
                    if tool_result.get('status') not in ['completed', 'error', 'timeout']:
                        # Si no terminó, marcar como error
                        self.results['tools_results'][tool_name]['status'] = 'error'
                        self.results['tools_results'][tool_name]['errors'] = 'Herramienta no completó correctamente'
                    
                    print(f"{tool_name} completado con estado: {tool_result.get('status')}")
                    
                except Exception as e:
                    print(f"Error ejecutando {tool_name}: {str(e)}")
                    self.results['tools_results'][tool_name] = {
                        'status': 'error',
                        'errors': str(e),
                        'output': ''
                    }
            
            # Verificar que todas las herramientas han terminado
            all_completed = True
            for tool_name, _ in tools_to_run:
                tool_status = self.results['tools_results'].get(tool_name, {}).get('status')
                if tool_status not in ['completed', 'error', 'timeout']:
                    all_completed = False
                    print(f"Advertencia: {tool_name} no completó correctamente (estado: {tool_status})")
            
            # Solo generar recomendaciones si todo terminó
            if all_completed:
                print("Generando recomendaciones...")
                self.generate_recommendations()
                self.results['status'] = 'completed'
            else:
                self.results['status'] = 'completed_with_errors'
            
            self.results['end_time'] = datetime.now().isoformat()
            print(f"Análisis terminado para: {self.target} con estado: {self.results['status']}")
            
        except Exception as e:
            self.results['status'] = 'error'
            self.results['error'] = str(e)
            self.results['end_time'] = datetime.now().isoformat()
            print(f"Error crítico en análisis: {str(e)}")

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/scan', methods=['POST'])
def start_scan():
    data = request.get_json()
    target = data.get('target', '').strip()
    
    if not target:
        return jsonify({'error': 'Target is required'}), 400
    
    # Validar formato del target - más permisivo
    if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-._]{0,253}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$', target):
        return jsonify({'error': f'Formato de dominio inválido: {target}. Usa formato como ejemplo.com'}), 400
    
    # Generar ID único para el análisis
    scan_id = str(uuid.uuid4())
    
    # Crear scanner
    scanner = SecurityScanner(target, scan_id)
    active_scans[scan_id] = scanner
    
    # Ejecutar análisis en hilo separado
    thread = threading.Thread(target=scanner.run_full_scan)
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'scan_id': scan_id,
        'message': 'Análisis iniciado correctamente'
    })

@app.route('/status/<scan_id>')
def get_scan_status(scan_id):
    if scan_id not in active_scans:
        return jsonify({'error': 'Scan not found'}), 404
    
    scanner = active_scans[scan_id]
    return jsonify(scanner.results)

@app.route('/report/<scan_id>')
def generate_report(scan_id):
    if scan_id not in active_scans:
        return jsonify({'error': 'Scan not found'}), 404
    
    scanner = active_scans[scan_id]
    
    # Permitir generar reporte si está completado o completado con errores
    if scanner.results['status'] not in ['completed', 'completed_with_errors']:
        return jsonify({'error': 'Scan not completed yet'}), 400
    
    # Generar reporte HTML
    report_html = render_template('report.html', results=scanner.results)
    
    # Guardar reporte
    report_filename = f"security_report_{scan_id}_{scanner.target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    report_path = os.path.join(REPORTS_DIR, report_filename)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_html)
    
    return send_file(report_path, as_attachment=True, download_name=report_filename)

if __name__ == '__main__':
    # Crear directorio de reportes si no existe
    os.makedirs(REPORTS_DIR, exist_ok=True)
    
    print("=" * 50)
    print("Security Scanner Web Interface")
    print("=" * 50)
    print("Iniciando servidor...")
    print("Acceder a: http://localhost:3333")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=3333)

