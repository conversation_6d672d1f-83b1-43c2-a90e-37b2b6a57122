# Security Scanner Web Interface - Configuración de ejemplo
# Copia este archivo como 'config.py' y personaliza según tus necesidades

# Configuración de Flask
FLASK_CONFIG = {
    'host': '0.0.0.0',  # Cambiar a '127.0.0.1' para acceso solo local
    'port': 5000,       # Puerto del servidor web
    'debug': True,      # Cambiar a False en producción
    'secret_key': 'security_scanner_2024_change_this_key'  # Cambiar por una clave segura
}

# Configuración de herramientas
TOOLS_CONFIG = {
    'nuclei': {
        'command_template': 'nuclei -target {target} -json -silent',
        'timeout': 300,  # 5 minutos
        'enabled': True
    },
    'theharvester': {
        'command_template': 'theharvester -d {target} -l 150 -b crtsh,hackertarget,duckduckgo,bing,sublist3r',
        'timeout': 180,  # 3 minutos
        'enabled': True
    },
    'amass': {
        'command_template': 'amass enum -passive -d {target} -timeout 5',
        'timeout': 300,  # 5 minutos
        'enabled': True
    },
    'whois': {
        'command_template': 'whois {target}',
        'timeout': 60,   # 1 minuto
        'enabled': True
    },
    'dig': {
        'commands': {
            'A': 'dig A {target} +short',
            'AAAA': 'dig AAAA {target} +short',
            'MX': 'dig MX {target} +short',
            'NS': 'dig NS {target} +short',
            'TXT': 'dig TXT {target} +short'
        },
        'timeout': 30,   # 30 segundos por consulta
        'enabled': True
    }
}

# Configuración de reportes
REPORTS_CONFIG = {
    'directory': 'reports',
    'max_reports': 100,  # Máximo número de reportes a mantener
    'auto_cleanup': True,  # Limpiar reportes antiguos automáticamente
    'include_raw_output': True  # Incluir salida cruda de herramientas en reportes
}

# Configuración de seguridad
SECURITY_CONFIG = {
    'max_concurrent_scans': 3,  # Máximo número de escaneos simultáneos
    'rate_limit': {
        'enabled': True,
        'requests_per_minute': 10  # Límite de solicitudes por minuto por IP
    },
    'allowed_domains_only': False,  # Si True, solo permitir dominios en la lista
    'allowed_domains': [],  # Lista de dominios permitidos
    'blocked_domains': [  # Dominios bloqueados por defecto
        'localhost',
        '127.0.0.1',
        '::1',
        'internal',
        'local'
    ]
}

# Configuración de logging
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'file': 'security_scanner.log',
    'max_size': 10485760,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# Configuración de notificaciones (futuras mejoras)
NOTIFICATIONS_CONFIG = {
    'email': {
        'enabled': False,
        'smtp_server': '',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'from_address': '',
        'to_addresses': []
    },
    'webhook': {
        'enabled': False,
        'url': '',
        'on_scan_complete': True,
        'on_vulnerabilities_found': True
    }
}

# Configuración de la interfaz web
UI_CONFIG = {
    'title': 'Security Scanner Pro',
    'theme': 'default',  # default, dark, light
    'show_tool_status': True,
    'auto_refresh_interval': 2000,  # milisegundos
    'max_vulnerabilities_display': 50,
    'max_subdomains_display': 20,
    'max_emails_display': 10
}

# Configuración de base de datos (para futuras mejoras)
DATABASE_CONFIG = {
    'enabled': False,
    'type': 'sqlite',  # sqlite, postgresql, mysql
    'host': 'localhost',
    'port': 5432,
    'database': 'security_scanner',
    'username': '',
    'password': '',
    'connection_string': 'sqlite:///security_scanner.db'
}

# Configuración de caché
CACHE_CONFIG = {
    'enabled': False,
    'type': 'memory',  # memory, redis, memcached
    'ttl': 3600,  # Tiempo de vida en segundos (1 hora)
    'redis_url': 'redis://localhost:6379/0'
}

