#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que todas las herramientas funcionan correctamente
"""

import subprocess
import json
import time

def run_command_with_timeout(command, timeout=30):
    """Ejecuta un comando con timeout"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 124, "", "Timeout"
    except Exception as e:
        return 1, "", str(e)

def test_nuclei():
    """Prueba Nuclei con configuración optimizada"""
    print("\n🧪 Probando Nuclei...")
    
    # Comando optimizado similar al usado en la aplicación
    command = "nuclei -target https://httpbin.org -tags tech -silent -timeout 10 -retries 1 -c 2"
    print(f"   Comando: {command}")
    
    start_time = time.time()
    returncode, stdout, stderr = run_command_with_timeout(command, timeout=30)
    end_time = time.time()
    
    print(f"   ⏱️  Tiempo: {end_time - start_time:.2f}s")
    print(f"   📊 Return code: {returncode}")
    
    if returncode == 0 or returncode == 1:  # 1 puede ser normal si no encuentra vulnerabilidades
        print("   ✅ Nuclei funciona correctamente")
        if stdout:
            print(f"   📄 Salida: {len(stdout)} caracteres")
        return True
    else:
        print(f"   ❌ Error en Nuclei: {stderr[:200]}")
        return False

def test_theharvester():
    """Prueba theHarvester con fuentes válidas"""
    print("\n🧪 Probando theHarvester...")
    
    # Comando optimizado con fuentes válidas
    command = "theharvester -d example.com -l 10 -b bing,crtsh,hackertarget"
    print(f"   Comando: {command}")
    
    start_time = time.time()
    returncode, stdout, stderr = run_command_with_timeout(command, timeout=60)
    end_time = time.time()
    
    print(f"   ⏱️  Tiempo: {end_time - start_time:.2f}s")
    print(f"   📊 Return code: {returncode}")
    
    if returncode == 0:
        print("   ✅ theHarvester funciona correctamente")
        # Buscar indicadores de éxito
        if "example.com" in stdout:
            print("   📧 Encontró hosts relacionados")
        if "emails found" in stdout or "No emails found" in stdout:
            print("   🔍 Búsqueda de emails completada")
        return True
    else:
        print(f"   ❌ Error en theHarvester: {stderr[:200]}")
        return False

def test_assetfinder():
    """Prueba Assetfinder"""
    print("\n🧪 Probando Assetfinder...")
    
    command = "assetfinder --subs-only example.com | head -10"
    print(f"   Comando: {command}")
    
    start_time = time.time()
    returncode, stdout, stderr = run_command_with_timeout(command, timeout=30)
    end_time = time.time()
    
    print(f"   ⏱️  Tiempo: {end_time - start_time:.2f}s")
    print(f"   📊 Return code: {returncode}")
    
    if returncode == 0 and stdout:
        subdomain_count = len([line for line in stdout.split('\n') if line.strip()])
        print(f"   ✅ Assetfinder funciona - encontró {subdomain_count} subdominios")
        return True
    else:
        print(f"   ❌ Error en Assetfinder: {stderr[:100]}")
        return False

def test_basic_tools():
    """Prueba herramientas básicas"""
    print("\n🧪 Probando herramientas básicas...")
    
    # Test dig
    print("   🔍 Probando dig...")
    returncode, stdout, stderr = run_command_with_timeout("dig A google.com +short +time=2", timeout=10)
    if returncode == 0 and stdout.strip():
        print("   ✅ dig funciona correctamente")
    else:
        print(f"   ❌ dig tiene problemas: {stderr[:100]}")
    
    # Test whois (con dominio que sabemos que existe)
    print("   🔍 Probando whois...")
    returncode, stdout, stderr = run_command_with_timeout("whois google.com | head -5", timeout=15)
    if returncode == 0 and "google" in stdout.lower():
        print("   ✅ whois funciona correctamente")
    else:
        print("   ⚠️  whois podría tener limitaciones pero es funcional")

def main():
    print("🧪 Test Suite - Herramientas de Seguridad Reparadas")
    print("=" * 60)
    
    # Ejecutar todas las pruebas
    tests = [
        ("Herramientas básicas", test_basic_tools),
        ("Nuclei", test_nuclei),
        ("theHarvester", test_theharvester),
        ("Assetfinder", test_assetfinder)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🎯 Ejecutando: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"   ❌ Error inesperado: {e}")
            results[test_name] = False
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status_icon = "✅" if passed else "❌"
        status_text = "PASSED" if passed else "FAILED"
        print(f"{status_icon} {test_name}: {status_text}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ¡Todas las herramientas funcionan correctamente!")
        print("   Tu aplicación está lista para usar.")
    else:
        print("⚠️  Algunas herramientas necesitan atención adicional.")
        print("   La aplicación funcionará con las herramientas disponibles.")
    
    print("\n💡 Para iniciar la aplicación web: python3 app.py")

if __name__ == "__main__":
    main()

